package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser.AbiEventInput;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser.ContractAbiEvent;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;

class AbiParserTest {

    @Mock
    private BcmonitoringConfigurationProperties properties;

    private AbiParser abiParser;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        abiParser = new AbiParser(properties);
    }

    @Test
    void testParseAbi_WithParameterNames() throws IOException {
        // Sample ABI JSON with event containing parameters with names
        String abiJson = """
            [
              {
                "type": "event",
                "name": "Transfer",
                "inputs": [
                  {
                    "name": "from",
                    "type": "address",
                    "indexed": true
                  },
                  {
                    "name": "to", 
                    "type": "address",
                    "indexed": true
                  },
                  {
                    "name": "amount",
                    "type": "uint256",
                    "indexed": false
                  }
                ]
              }
            ]
            """;

        // Parse the ABI
        Map<String, ContractAbiEvent> events = abiParser.parseAbi(abiJson);

        // Verify that events were parsed
        assertFalse(events.isEmpty());
        assertEquals(1, events.size());

        // Get the contract ABI event
        ContractAbiEvent contractAbiEvent = events.values().iterator().next();
        assertNotNull(contractAbiEvent);
        assertNotNull(contractAbiEvent.getEvent());
        assertEquals("Transfer", contractAbiEvent.getEvent().getName());

        // Verify inputs with names
        assertEquals(3, contractAbiEvent.getInputs().size());

        AbiEventInput input1 = contractAbiEvent.getInputs().get(0);
        assertEquals("from", input1.getName());
        assertTrue(input1.isIndexed());

        AbiEventInput input2 = contractAbiEvent.getInputs().get(1);
        assertEquals("to", input2.getName());
        assertTrue(input2.isIndexed());

        AbiEventInput input3 = contractAbiEvent.getInputs().get(2);
        assertEquals("amount", input3.getName());
        assertFalse(input3.isIndexed());
    }

    @Test
    void testParseAbi_EmptyContent() throws IOException {
        Map<String, ContractAbiEvent> events = abiParser.parseAbi("");
        assertTrue(events.isEmpty());
    }

    @Test
    void testParseAbi_NullContent() throws IOException {
        Map<String, ContractAbiEvent> events = abiParser.parseAbi(null);
        assertTrue(events.isEmpty());
    }
}
