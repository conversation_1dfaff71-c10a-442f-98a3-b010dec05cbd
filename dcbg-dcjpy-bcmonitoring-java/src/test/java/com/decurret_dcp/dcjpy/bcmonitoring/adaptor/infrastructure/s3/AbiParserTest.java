package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser.EnhancedEvent;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser.EventParameter;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class AbiParserTest {

    @Mock
    private BcmonitoringConfigurationProperties properties;

    private AbiParser abiParser;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        abiParser = new AbiParser(properties);
    }

    @Test
    void testParseAbi_WithParameterNames() throws IOException {
        // Sample ABI JSON with event containing parameters with names
        String abiJson = """
            [
              {
                "type": "event",
                "name": "Transfer",
                "inputs": [
                  {
                    "name": "from",
                    "type": "address",
                    "indexed": true
                  },
                  {
                    "name": "to", 
                    "type": "address",
                    "indexed": true
                  },
                  {
                    "name": "amount",
                    "type": "uint256",
                    "indexed": false
                  }
                ]
              }
            ]
            """;

        // Parse the ABI
        Map<String, EnhancedEvent> events = abiParser.parseAbi(abiJson);

        // Verify that events were parsed
        assertFalse(events.isEmpty());
        assertEquals(1, events.size());

        // Get the enhanced event
        EnhancedEvent enhancedEvent = events.values().iterator().next();
        assertNotNull(enhancedEvent);
        assertNotNull(enhancedEvent.getEvent());
        assertEquals("Transfer", enhancedEvent.getEvent().getName());

        // Verify parameters with names
        assertEquals(3, enhancedEvent.getParameters().size());

        EventParameter param1 = enhancedEvent.getParameters().get(0);
        assertEquals("from", param1.getName());
        assertTrue(param1.isIndexed());

        EventParameter param2 = enhancedEvent.getParameters().get(1);
        assertEquals("to", param2.getName());
        assertTrue(param2.isIndexed());

        EventParameter param3 = enhancedEvent.getParameters().get(2);
        assertEquals("amount", param3.getName());
        assertFalse(param3.isIndexed());
    }

    @Test
    void testParseAbi_EmptyContent() throws IOException {
        Map<String, EnhancedEvent> events = abiParser.parseAbi("");
        assertTrue(events.isEmpty());
    }

    @Test
    void testParseAbi_NullContent() throws IOException {
        Map<String, EnhancedEvent> events = abiParser.parseAbi(null);
        assertTrue(events.isEmpty());
    }
}
